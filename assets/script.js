/**
 * Solomon Islands Scholarship Application - JavaScript
 * Handles form validation, step navigation, and user interactions
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded, initializing form...');

    // Initialize form functionality
    initializeForm();
    initializeValidation();
    initializeStepNavigation();

    console.log('Form initialization complete');
});

/**
 * Initialize form functionality
 */
function initializeForm() {
    const form = document.getElementById('scholarshipForm');
    const steps = document.querySelectorAll('.form-step');
    const progressBar = document.querySelector('.progress-bar');
    
    // Set initial step
    let currentStep = 1;
    const totalSteps = steps.length;
    
    // Update progress bar
    updateProgressBar(currentStep, totalSteps);
    
    // Store form state
    window.scholarshipForm = {
        currentStep: currentStep,
        totalSteps: totalSteps,
        form: form,
        steps: steps,
        progressBar: progressBar
    };
}

/**
 * Initialize step navigation
 */
function initializeStepNavigation() {
    const nextBtn = document.getElementById('nextBtn');
    const prevBtn = document.getElementById('prevBtn');
    const submitBtn = document.getElementById('submitBtn');

    console.log('Navigation elements found:', {
        nextBtn: !!nextBtn,
        prevBtn: !!prevBtn,
        submitBtn: !!submitBtn
    });

    if (!nextBtn) {
        console.error('Next button not found!');
        return;
    }

    // Next button click handler
    nextBtn.addEventListener('click', function(e) {
        console.log('Next button clicked');
        e.preventDefault();

        if (validateCurrentStep()) {
            console.log('Validation passed, moving to next step');
            nextStep();
        } else {
            console.log('Validation failed');
        }
    });
    
    // Previous button click handler
    prevBtn.addEventListener('click', function() {
        prevStep();
    });
    
    // Form submission handler
    window.scholarshipForm.form.addEventListener('submit', function(e) {
        if (!validateAllSteps()) {
            e.preventDefault();
            showAlert('Please complete all required fields before submitting.', 'danger');
            return false;
        }
        
        // Show loading state
        submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Submitting...';
        submitBtn.disabled = true;
    });
}

/**
 * Move to next step
 */
function nextStep() {
    const { currentStep, totalSteps, steps } = window.scholarshipForm;
    
    if (currentStep < totalSteps) {
        // Hide current step
        steps[currentStep - 1].classList.remove('active');
        
        // Show next step
        window.scholarshipForm.currentStep++;
        steps[window.scholarshipForm.currentStep - 1].classList.add('active');
        
        // Update navigation buttons
        updateNavigationButtons();
        
        // Update progress bar
        updateProgressBar(window.scholarshipForm.currentStep, totalSteps);
        
        // Scroll to top
        window.scrollTo({ top: 0, behavior: 'smooth' });
    }
}

/**
 * Move to previous step
 */
function prevStep() {
    const { currentStep, steps } = window.scholarshipForm;
    
    if (currentStep > 1) {
        // Hide current step
        steps[currentStep - 1].classList.remove('active');
        
        // Show previous step
        window.scholarshipForm.currentStep--;
        steps[window.scholarshipForm.currentStep - 1].classList.add('active');
        
        // Update navigation buttons
        updateNavigationButtons();
        
        // Update progress bar
        updateProgressBar(window.scholarshipForm.currentStep, window.scholarshipForm.totalSteps);
        
        // Scroll to top
        window.scrollTo({ top: 0, behavior: 'smooth' });
    }
}

/**
 * Update navigation buttons visibility
 */
function updateNavigationButtons() {
    const { currentStep, totalSteps } = window.scholarshipForm;
    const nextBtn = document.getElementById('nextBtn');
    const prevBtn = document.getElementById('prevBtn');
    const submitBtn = document.getElementById('submitBtn');
    
    // Show/hide previous button
    prevBtn.style.display = currentStep > 1 ? 'block' : 'none';
    
    // Show/hide next and submit buttons
    if (currentStep === totalSteps) {
        nextBtn.style.display = 'none';
        submitBtn.style.display = 'block';
    } else {
        nextBtn.style.display = 'block';
        submitBtn.style.display = 'none';
    }
}

/**
 * Update progress bar
 */
function updateProgressBar(current, total) {
    const { progressBar } = window.scholarshipForm;
    const percentage = (current / total) * 100;
    progressBar.style.width = percentage + '%';
    progressBar.setAttribute('aria-valuenow', percentage);
}

/**
 * Initialize form validation
 */
function initializeValidation() {
    // Real-time validation for email
    const emailInput = document.getElementById('email');
    if (emailInput) {
        emailInput.addEventListener('blur', function() {
            validateEmail(this);
        });
    }
    
    // Real-time validation for phone
    const phoneInput = document.getElementById('phone');
    if (phoneInput) {
        phoneInput.addEventListener('blur', function() {
            validatePhone(this);
        });
    }
    
    // Real-time validation for required fields
    const requiredFields = document.querySelectorAll('[required]');
    requiredFields.forEach(field => {
        field.addEventListener('blur', function() {
            validateField(this);
        });
    });
}

/**
 * Validate current step
 */
function validateCurrentStep() {
    const { currentStep, steps } = window.scholarshipForm;
    const currentStepElement = steps[currentStep - 1];
    const requiredFields = currentStepElement.querySelectorAll('[required]');
    let isValid = true;
    
    // Validate required fields in current step
    requiredFields.forEach(field => {
        if (!validateField(field)) {
            isValid = false;
        }
    });
    
    // Special validation for checkboxes groups
    if (currentStep === 2) {
        // Validate province of origin
        const provinceOriginChecked = currentStepElement.querySelectorAll('input[name="province_of_origin[]"]:checked');
        if (provinceOriginChecked.length === 0) {
            showFieldError('province_origin_error', 'Please select at least one province of origin.');
            isValid = false;
        } else {
            hideFieldError('province_origin_error');
        }
    }
    
    if (currentStep === 4) {
        // Validate HEI selection
        const heiChecked = currentStepElement.querySelectorAll('input[name="hei_name[]"]:checked');
        if (heiChecked.length === 0) {
            showFieldError('hei_error', 'Please select at least one Higher Education Institution.');
            isValid = false;
        } else {
            hideFieldError('hei_error');
        }
    }
    
    return isValid;
}

/**
 * Validate all steps
 */
function validateAllSteps() {
    let isValid = true;
    const { totalSteps } = window.scholarshipForm;
    
    for (let i = 1; i <= totalSteps; i++) {
        const originalStep = window.scholarshipForm.currentStep;
        window.scholarshipForm.currentStep = i;
        
        if (!validateCurrentStep()) {
            isValid = false;
        }
        
        window.scholarshipForm.currentStep = originalStep;
    }
    
    return isValid;
}

/**
 * Validate individual field
 */
function validateField(field) {
    const value = field.value.trim();
    let isValid = true;
    
    // Check if required field is empty
    if (field.hasAttribute('required') && !value) {
        field.classList.add('is-invalid');
        field.classList.remove('is-valid');
        isValid = false;
    } else {
        // Field-specific validation
        if (field.type === 'email' && value) {
            isValid = validateEmail(field);
        } else if (field.type === 'tel' && value) {
            isValid = validatePhone(field);
        } else if (field.type === 'radio' && field.hasAttribute('required')) {
            const radioGroup = document.querySelectorAll(`input[name="${field.name}"]`);
            const isChecked = Array.from(radioGroup).some(radio => radio.checked);
            if (!isChecked) {
                radioGroup.forEach(radio => radio.classList.add('is-invalid'));
                isValid = false;
            } else {
                radioGroup.forEach(radio => {
                    radio.classList.remove('is-invalid');
                    radio.classList.add('is-valid');
                });
            }
        } else {
            field.classList.remove('is-invalid');
            field.classList.add('is-valid');
        }
    }
    
    return isValid;
}

/**
 * Validate email field
 */
function validateEmail(field) {
    const email = field.value.trim();
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    
    if (email && !emailRegex.test(email)) {
        field.classList.add('is-invalid');
        field.classList.remove('is-valid');
        return false;
    } else if (email) {
        field.classList.remove('is-invalid');
        field.classList.add('is-valid');
    }
    
    return true;
}

/**
 * Validate phone field
 */
function validatePhone(field) {
    const phone = field.value.trim();
    const phoneRegex = /^[\+]?[0-9\s\-\(\)]{7,20}$/;
    
    if (phone && !phoneRegex.test(phone)) {
        field.classList.add('is-invalid');
        field.classList.remove('is-valid');
        return false;
    } else if (phone) {
        field.classList.remove('is-invalid');
        field.classList.add('is-valid');
    }
    
    return true;
}

/**
 * Show field error
 */
function showFieldError(elementId, message) {
    const errorElement = document.getElementById(elementId);
    if (errorElement) {
        errorElement.textContent = message;
        errorElement.style.display = 'block';
    }
}

/**
 * Hide field error
 */
function hideFieldError(elementId) {
    const errorElement = document.getElementById(elementId);
    if (errorElement) {
        errorElement.style.display = 'none';
    }
}

/**
 * Show alert message
 */
function showAlert(message, type = 'info') {
    const alertHtml = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            <i class="fas fa-${type === 'danger' ? 'exclamation-triangle' : 'info-circle'} me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    const container = document.querySelector('.container');
    const existingAlert = container.querySelector('.alert');
    
    if (existingAlert) {
        existingAlert.remove();
    }
    
    container.insertAdjacentHTML('afterbegin', alertHtml);
    
    // Auto-dismiss after 5 seconds
    setTimeout(() => {
        const alert = container.querySelector('.alert');
        if (alert) {
            alert.remove();
        }
    }, 5000);
}
