<?php
/**
 * Solomon Islands Scholarship Application - Setup Script
 * 
 * This script helps with the initial setup of the application
 * Run this once after uploading files to your server
 */

// Check if setup has already been run
if (file_exists('.env') && file_exists('logs/setup_complete.flag')) {
    die('Setup has already been completed. Delete logs/setup_complete.flag to run setup again.');
}

$errors = [];
$warnings = [];
$success = [];

echo "<!DOCTYPE html>
<html lang='en'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Solomon Islands Scholarship Application - Setup</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>
</head>
<body>
<div class='container py-5'>
    <div class='row justify-content-center'>
        <div class='col-lg-8'>
            <div class='card'>
                <div class='card-header bg-primary text-white'>
                    <h3 class='mb-0'>Solomon Islands Scholarship Application - Setup</h3>
                </div>
                <div class='card-body'>";

// Check PHP version
if (version_compare(PHP_VERSION, '7.4.0', '<')) {
    $errors[] = "PHP 7.4 or higher is required. Current version: " . PHP_VERSION;
} else {
    $success[] = "PHP version: " . PHP_VERSION . " ✓";
}

// Check required extensions
$required_extensions = ['mysqli', 'session', 'json', 'mbstring'];
foreach ($required_extensions as $ext) {
    if (!extension_loaded($ext)) {
        $errors[] = "Required PHP extension missing: $ext";
    } else {
        $success[] = "PHP extension $ext is loaded ✓";
    }
}

// Check if .env file exists
if (!file_exists('.env')) {
    if (file_exists('.env.example')) {
        if (copy('.env.example', '.env')) {
            $success[] = "Created .env file from template ✓";
        } else {
            $errors[] = "Could not create .env file. Please copy .env.example to .env manually.";
        }
    } else {
        $errors[] = ".env.example file not found. Please create .env file manually.";
    }
} else {
    $success[] = ".env file exists ✓";
}

// Check directory permissions
$directories = ['logs', 'uploads'];
foreach ($directories as $dir) {
    if (!is_dir($dir)) {
        if (mkdir($dir, 0755, true)) {
            $success[] = "Created directory: $dir ✓";
        } else {
            $errors[] = "Could not create directory: $dir";
        }
    } else {
        $success[] = "Directory exists: $dir ✓";
    }
    
    if (is_dir($dir) && !is_writable($dir)) {
        $warnings[] = "Directory $dir is not writable. Please set permissions to 755 or 777.";
    }
}

// Test database connection if .env exists
if (file_exists('.env')) {
    try {
        require_once 'env_loader.php';
        EnvLoader::load('.env');
        
        $host = EnvLoader::get('DB_HOST');
        $username = EnvLoader::get('DB_USERNAME');
        $password = EnvLoader::get('DB_PASSWORD');
        $database = EnvLoader::get('DB_NAME');
        
        if ($host && $username && $database) {
            $conn = new mysqli($host, $username, $password, $database);
            
            if ($conn->connect_error) {
                $errors[] = "Database connection failed: " . $conn->connect_error;
            } else {
                $success[] = "Database connection successful ✓";
                
                // Check if tables exist
                $tables = ['applications', 'admin_users', 'admin_sessions', 'application_status_log'];
                $existing_tables = [];
                
                foreach ($tables as $table) {
                    $result = $conn->query("SHOW TABLES LIKE '$table'");
                    if ($result && $result->num_rows > 0) {
                        $existing_tables[] = $table;
                    }
                }
                
                if (count($existing_tables) === count($tables)) {
                    $success[] = "All database tables exist ✓";
                } else {
                    $missing_tables = array_diff($tables, $existing_tables);
                    $warnings[] = "Missing database tables: " . implode(', ', $missing_tables) . ". Please run schema.sql";
                }
                
                $conn->close();
            }
        } else {
            $warnings[] = "Database credentials not configured in .env file";
        }
    } catch (Exception $e) {
        $errors[] = "Error testing database connection: " . $e->getMessage();
    }
}

// Display results
if (!empty($success)) {
    echo "<div class='alert alert-success'><h5>✓ Success</h5><ul class='mb-0'>";
    foreach ($success as $msg) {
        echo "<li>$msg</li>";
    }
    echo "</ul></div>";
}

if (!empty($warnings)) {
    echo "<div class='alert alert-warning'><h5>⚠ Warnings</h5><ul class='mb-0'>";
    foreach ($warnings as $msg) {
        echo "<li>$msg</li>";
    }
    echo "</ul></div>";
}

if (!empty($errors)) {
    echo "<div class='alert alert-danger'><h5>✗ Errors</h5><ul class='mb-0'>";
    foreach ($errors as $msg) {
        echo "<li>$msg</li>";
    }
    echo "</ul></div>";
} else {
    // Create setup complete flag
    if (!is_dir('logs')) {
        mkdir('logs', 0755, true);
    }
    file_put_contents('logs/setup_complete.flag', date('Y-m-d H:i:s'));
    
    echo "<div class='alert alert-success'>
        <h5>🎉 Setup Complete!</h5>
        <p>Your Solomon Islands Scholarship Application system is ready to use.</p>
        <p><strong>Next Steps:</strong></p>
        <ol>
            <li>Update your .env file with the correct database credentials</li>
            <li>Import the database schema: <code>mysql -u username -p database_name < schema.sql</code></li>
            <li>Access the application: <a href='index.php' class='btn btn-primary btn-sm'>Application Form</a></li>
            <li>Access admin panel: <a href='admin/login.php' class='btn btn-secondary btn-sm'>Admin Login</a></li>
            <li><strong>Important:</strong> Delete this setup.php file for security</li>
        </ol>
    </div>";
}

echo "
                <div class='mt-4'>
                    <h5>Default Admin Credentials</h5>
                    <div class='alert alert-info'>
                        <strong>Username:</strong> admin<br>
                        <strong>Password:</strong> admin123<br>
                        <small class='text-danger'>⚠️ Change these credentials immediately after first login!</small>
                    </div>
                </div>
                
                <div class='mt-4'>
                    <h5>Security Checklist</h5>
                    <ul class='list-group'>
                        <li class='list-group-item'>✓ .env file is protected by .htaccess</li>
                        <li class='list-group-item'>✓ Sensitive files are blocked from web access</li>
                        <li class='list-group-item'>⚠️ Change default admin password</li>
                        <li class='list-group-item'>⚠️ Enable HTTPS in production</li>
                        <li class='list-group-item'>⚠️ Set APP_DEBUG=false in .env for production</li>
                        <li class='list-group-item'>⚠️ Delete setup.php after setup is complete</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
</body>
</html>";
?>
