<?php
require_once '../config.php';

// Require admin login
requireAdminLogin();

// Check if request is valid
if ($_SERVER['REQUEST_METHOD'] !== 'POST' && !isset($_GET['id'])) {
    showError('Invalid request.');
    redirect('index.php');
}

// Get parameters
$application_id = intval($_POST['id'] ?? $_GET['id'] ?? 0);
$new_status = sanitizeInput($_POST['status'] ?? $_GET['status'] ?? '');
$notes = sanitizeInput($_POST['notes'] ?? '');

// Validate inputs
if ($application_id <= 0) {
    showError('Invalid application ID.');
    redirect('index.php');
}

$valid_statuses = ['Selected', 'Pending', 'Under Review', 'Rejected'];
if (!in_array($new_status, $valid_statuses)) {
    showError('Invalid status.');
    redirect('index.php');
}

try {
    // Get database connection
    $db = Database::getInstance();
    $conn = $db->getConnection();
    
    // Get current application status
    $sql = "SELECT selection_status, full_name FROM applications WHERE id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("i", $application_id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows === 0) {
        showError('Application not found.');
        redirect('index.php');
    }
    
    $application = $result->fetch_assoc();
    $old_status = $application['selection_status'];
    $student_name = $application['full_name'];
    
    // Check if status is actually changing
    if ($old_status === $new_status) {
        showError('Status is already set to ' . $new_status . '.');
        redirect('index.php');
    }
    
    // Begin transaction
    $conn->begin_transaction();
    
    try {
        // Update application status
        $update_sql = "UPDATE applications SET selection_status = ?, updated_at = NOW() WHERE id = ?";
        $update_stmt = $conn->prepare($update_sql);
        $update_stmt->bind_param("si", $new_status, $application_id);
        
        if (!$update_stmt->execute()) {
            throw new Exception("Failed to update application status");
        }
        
        // Log the status change
        $log_sql = "INSERT INTO application_status_log (application_id, old_status, new_status, changed_by, notes) VALUES (?, ?, ?, ?, ?)";
        $log_stmt = $conn->prepare($log_sql);
        $admin_id = getAdminUserId();
        $log_stmt->bind_param("issss", $application_id, $old_status, $new_status, $admin_id, $notes);
        
        if (!$log_stmt->execute()) {
            throw new Exception("Failed to log status change");
        }
        
        // Commit transaction
        $conn->commit();
        
        // Log activity
        $admin_username = getAdminUsername();
        logActivity("Status updated for application ID $application_id ($student_name): $old_status → $new_status by $admin_username", 'INFO');
        
        // Show success message
        showSuccess("Application status updated successfully from '$old_status' to '$new_status'.");
        
        // Send notification email if status is Selected or Rejected and email is available
        sendStatusNotificationEmail($application_id, $new_status);
        
    } catch (Exception $e) {
        // Rollback transaction
        $conn->rollback();
        throw $e;
    }
    
} catch (Exception $e) {
    logActivity("Failed to update status for application ID $application_id: " . $e->getMessage(), 'ERROR');
    showError('An error occurred while updating the status. Please try again.');
}

// Redirect back to admin dashboard
redirect('index.php');

/**
 * Send status notification email to applicant
 */
function sendStatusNotificationEmail($application_id, $status) {
    try {
        $db = Database::getInstance();
        $conn = $db->getConnection();
        
        // Get application details
        $sql = "SELECT full_name, email, student_code FROM applications WHERE id = ? AND email IS NOT NULL AND email != ''";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("i", $application_id);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result->num_rows === 0) {
            return; // No email to send to
        }
        
        $app = $result->fetch_assoc();
        $reference_number = "SI-" . date('Y') . "-" . str_pad($application_id, 6, '0', STR_PAD_LEFT);
        
        $subject = "Solomon Islands Scholarship Application Status Update";
        
        // Customize message based on status
        $status_message = '';
        $status_color = '';
        
        switch ($status) {
            case 'Selected':
                $status_message = "Congratulations! Your scholarship application has been <strong>SELECTED</strong>. You will be contacted soon with further instructions.";
                $status_color = '#28a745';
                break;
            case 'Rejected':
                $status_message = "We regret to inform you that your scholarship application has been <strong>REJECTED</strong>. Thank you for your interest in the program.";
                $status_color = '#dc3545';
                break;
            case 'Under Review':
                $status_message = "Your scholarship application is currently <strong>UNDER REVIEW</strong>. We will notify you once a decision has been made.";
                $status_color = '#17a2b8';
                break;
            case 'Pending':
                $status_message = "Your scholarship application status has been updated to <strong>PENDING</strong>. Please wait for further updates.";
                $status_color = '#ffc107';
                break;
        }
        
        $message = "
        <html>
        <head>
            <title>Application Status Update</title>
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                .header { background-color: #0066cc; color: white; padding: 20px; text-align: center; }
                .content { padding: 20px; }
                .status-box { background-color: $status_color; color: white; padding: 15px; border-radius: 5px; margin: 20px 0; text-align: center; }
                .footer { background-color: #f8f9fa; padding: 15px; text-align: center; font-size: 12px; color: #666; }
            </style>
        </head>
        <body>
            <div class='header'>
                <h2>Solomon Islands Scholarship Program</h2>
                <p>Papua New Guinea Higher Education Initiative</p>
            </div>
            
            <div class='content'>
                <p>Dear {$app['full_name']},</p>
                
                <p>We are writing to inform you of an update to your scholarship application status.</p>
                
                <div class='status-box'>
                    <h3>Application Status Update</h3>
                    <p>$status_message</p>
                </div>
                
                <p><strong>Application Details:</strong></p>
                <ul>
                    <li>Reference Number: <strong>$reference_number</strong></li>
                    <li>Student Code: <strong>{$app['student_code']}</strong></li>
                    <li>Status Updated: " . date('F j, Y \a\t g:i A') . "</li>
                </ul>
                
                <p>If you have any questions about your application status, please contact us using your reference number.</p>
                
                <p>Best regards,<br>
                Solomon Islands Scholarship Program<br>
                Ministry of Education</p>
            </div>
            
            <div class='footer'>
                <p>&copy; 2024 Solomon Islands Government - Papua New Guinea Scholarship Program</p>
                <p>This is an automated message. Please do not reply to this email.</p>
            </div>
        </body>
        </html>
        ";
        
        $headers = "MIME-Version: 1.0" . "\r\n";
        $headers .= "Content-type:text/html;charset=UTF-8" . "\r\n";
        $headers .= "From: " . FROM_NAME . " <" . FROM_EMAIL . ">" . "\r\n";
        
        // Send email
        if (mail($app['email'], $subject, $message, $headers)) {
            logActivity("Status notification email sent to: {$app['email']} for application ID: $application_id", 'INFO');
        } else {
            logActivity("Failed to send status notification email to: {$app['email']} for application ID: $application_id", 'WARNING');
        }
        
    } catch (Exception $e) {
        logActivity("Email notification error for application ID $application_id: " . $e->getMessage(), 'ERROR');
    }
}
?>
