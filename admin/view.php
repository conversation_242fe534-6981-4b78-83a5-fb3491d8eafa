<?php
require_once '../config.php';

// Require admin login
requireAdminLogin();

// Get application ID
$application_id = intval($_GET['id'] ?? 0);

if ($application_id <= 0) {
    die('Invalid application ID');
}

// Get database connection
$db = Database::getInstance();
$conn = $db->getConnection();

// Get application details
$sql = "SELECT * FROM applications WHERE id = ?";
$stmt = $conn->prepare($sql);
$stmt->bind_param("i", $application_id);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows === 0) {
    die('Application not found');
}

$application = $result->fetch_assoc();
$reference_number = "SI-" . date('Y', strtotime($application['created_at'])) . "-" . str_pad($application_id, 6, '0', STR_PAD_LEFT);
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Application Details - <?php echo htmlspecialchars($application['full_name']); ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="../assets/style.css" rel="stylesheet">
    
    <style>
        @media print {
            .no-print { display: none !important; }
            .card { border: 1px solid #ddd !important; box-shadow: none !important; }
        }
    </style>
</head>
<body>
    <div class="container-fluid py-4">
        <!-- Header -->
        <div class="row mb-4 no-print">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <h2>
                        <i class="fas fa-file-alt me-2"></i>
                        Application Details
                    </h2>
                    <div>
                        <button onclick="window.print()" class="btn btn-primary me-2">
                            <i class="fas fa-print me-1"></i>Print
                        </button>
                        <button onclick="window.close()" class="btn btn-secondary">
                            <i class="fas fa-times me-1"></i>Close
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Application Information -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0">
                            <?php echo htmlspecialchars($application['full_name']); ?>
                            <span class="badge bg-light text-dark ms-2"><?php echo $reference_number; ?></span>
                        </h4>
                    </div>
                    
                    <div class="card-body">
                        <!-- Basic Information -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <h5 class="text-primary border-bottom pb-2">Basic Information</h5>
                                <table class="table table-sm">
                                    <tr>
                                        <td><strong>Student Code:</strong></td>
                                        <td><?php echo htmlspecialchars($application['student_code']); ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Selection Status:</strong></td>
                                        <td>
                                            <?php
                                            $status_class = [
                                                'Selected' => 'success',
                                                'Pending' => 'warning',
                                                'Under Review' => 'info',
                                                'Rejected' => 'danger'
                                            ];
                                            $class = $status_class[$application['selection_status']] ?? 'secondary';
                                            ?>
                                            <span class="badge bg-<?php echo $class; ?>">
                                                <?php echo htmlspecialchars($application['selection_status']); ?>
                                            </span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong>Date of Birth:</strong></td>
                                        <td><?php echo $application['date_of_birth'] ? date('F j, Y', strtotime($application['date_of_birth'])) : 'Not provided'; ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Gender:</strong></td>
                                        <td><?php echo htmlspecialchars($application['gender'] ?: 'Not specified'); ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Email:</strong></td>
                                        <td><?php echo htmlspecialchars($application['email'] ?: 'Not provided'); ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Phone:</strong></td>
                                        <td><?php echo htmlspecialchars($application['phone'] ?: 'Not provided'); ?></td>
                                    </tr>
                                </table>
                            </div>
                            
                            <div class="col-md-6">
                                <h5 class="text-primary border-bottom pb-2">Location Information</h5>
                                <table class="table table-sm">
                                    <tr>
                                        <td><strong>Province of Origin:</strong></td>
                                        <td><?php echo htmlspecialchars(str_replace(',', ', ', $application['province_of_origin'])); ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Current Residential Province:</strong></td>
                                        <td><?php echo htmlspecialchars($application['current_residential_province'] ? str_replace(',', ', ', $application['current_residential_province']) : 'Not provided'); ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Transport to Honiara:</strong></td>
                                        <td><?php echo htmlspecialchars($application['mode_of_transport_to_honiara']); ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Honiara-POM Route:</strong></td>
                                        <td><?php echo htmlspecialchars($application['honiara_pom_route'] ?: 'Not specified'); ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Travel Route Preference:</strong></td>
                                        <td><?php echo htmlspecialchars($application['travel_question'] ?: 'Not specified'); ?></td>
                                    </tr>
                                </table>
                            </div>
                        </div>

                        <!-- Education Information -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h5 class="text-primary border-bottom pb-2">Education Information</h5>
                                <div class="row">
                                    <div class="col-md-6">
                                        <table class="table table-sm">
                                            <tr>
                                                <td><strong>Higher Education Institution:</strong></td>
                                                <td><?php echo htmlspecialchars(str_replace(',', ', ', $application['hei_name'])); ?></td>
                                            </tr>
                                            <tr>
                                                <td><strong>PNG Province (Study Destination):</strong></td>
                                                <td><?php echo htmlspecialchars($application['png_province']); ?></td>
                                            </tr>
                                            <tr>
                                                <td><strong>Course of Study:</strong></td>
                                                <td><?php echo htmlspecialchars($application['course_of_study'] ?: 'Not specified'); ?></td>
                                            </tr>
                                            <tr>
                                                <td><strong>Estimated Cost:</strong></td>
                                                <td><?php echo $application['estimated_cost'] ? '$' . number_format($application['estimated_cost'], 2) : 'Not provided'; ?></td>
                                            </tr>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Academic Qualifications -->
                        <?php if (!empty($application['academic_qualifications'])): ?>
                        <div class="row mb-4">
                            <div class="col-12">
                                <h5 class="text-primary border-bottom pb-2">Academic Qualifications</h5>
                                <div class="card bg-light">
                                    <div class="card-body">
                                        <p class="mb-0"><?php echo nl2br(htmlspecialchars($application['academic_qualifications'])); ?></p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <?php endif; ?>

                        <!-- Motivation Letter -->
                        <?php if (!empty($application['motivation_letter'])): ?>
                        <div class="row mb-4">
                            <div class="col-12">
                                <h5 class="text-primary border-bottom pb-2">Motivation Letter</h5>
                                <div class="card bg-light">
                                    <div class="card-body">
                                        <p class="mb-0"><?php echo nl2br(htmlspecialchars($application['motivation_letter'])); ?></p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <?php endif; ?>

                        <!-- System Information -->
                        <div class="row">
                            <div class="col-12">
                                <h5 class="text-primary border-bottom pb-2">System Information</h5>
                                <table class="table table-sm">
                                    <tr>
                                        <td><strong>Application ID:</strong></td>
                                        <td><?php echo $application['id']; ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Reference Number:</strong></td>
                                        <td><?php echo $reference_number; ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Submitted:</strong></td>
                                        <td><?php echo date('F j, Y \a\t g:i A', strtotime($application['created_at'])); ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Last Updated:</strong></td>
                                        <td><?php echo date('F j, Y \a\t g:i A', strtotime($application['updated_at'])); ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>IP Address:</strong></td>
                                        <td><?php echo htmlspecialchars($application['ip_address']); ?></td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Actions -->
        <div class="row mt-4 no-print">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Actions</h5>
                    </div>
                    <div class="card-body">
                        <form method="POST" action="update_status.php" class="d-inline">
                            <input type="hidden" name="id" value="<?php echo $application['id']; ?>">
                            <div class="row align-items-end">
                                <div class="col-md-4">
                                    <label for="status" class="form-label">Update Status:</label>
                                    <select class="form-select" name="status" id="status">
                                        <option value="Selected" <?php echo $application['selection_status'] === 'Selected' ? 'selected' : ''; ?>>Selected</option>
                                        <option value="Pending" <?php echo $application['selection_status'] === 'Pending' ? 'selected' : ''; ?>>Pending</option>
                                        <option value="Under Review" <?php echo $application['selection_status'] === 'Under Review' ? 'selected' : ''; ?>>Under Review</option>
                                        <option value="Rejected" <?php echo $application['selection_status'] === 'Rejected' ? 'selected' : ''; ?>>Rejected</option>
                                    </select>
                                </div>
                                <div class="col-md-4">
                                    <label for="notes" class="form-label">Notes (optional):</label>
                                    <input type="text" class="form-control" name="notes" id="notes" placeholder="Add a note about this status change">
                                </div>
                                <div class="col-md-4">
                                    <button type="submit" class="btn btn-warning">
                                        <i class="fas fa-save me-1"></i>Update Status
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
