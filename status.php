<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Check Application Status - Solomon Islands Scholarship</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="assets/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <?php include 'includes/navbar.php'; ?>

    <!-- Page Header -->
    <section class="bg-primary text-white py-5" style="background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%) !important;">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center">
                    <h1 class="display-5 fw-bold mb-3">
                        <i class="fas fa-search me-3"></i>
                        Check Application Status
                    </h1>
                    <p class="lead mb-0">
                        Track your scholarship application progress
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- Main Content -->
    <main class="container my-5">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <!-- Status Check Form -->
                <div class="card shadow">
                    <div class="card-header bg-light">
                        <h3 class="card-title mb-0">
                            <i class="fas fa-file-search me-2"></i>
                            Application Status Lookup
                        </h3>
                    </div>
                    <div class="card-body">
                        <form id="statusForm">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="reference_number" class="form-label">
                                        Reference Number <span class="text-danger">*</span>
                                    </label>
                                    <input type="text" class="form-control" id="reference_number" 
                                           placeholder="SI-2024-000001" required>
                                    <div class="form-text">
                                        <i class="fas fa-info-circle me-1"></i>
                                        Format: SI-YYYY-XXXXXX (e.g., SI-2024-000001)
                                    </div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="student_code" class="form-label">
                                        Student Code <span class="text-danger">*</span>
                                    </label>
                                    <input type="text" class="form-control" id="student_code" 
                                           placeholder="Your student code" required>
                                </div>
                            </div>
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="fas fa-search me-2"></i>Check Status
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Status Result (Hidden by default) -->
                <div id="statusResult" class="card shadow mt-4" style="display: none;">
                    <div class="card-header bg-success text-white">
                        <h4 class="mb-0">
                            <i class="fas fa-check-circle me-2"></i>
                            Application Found
                        </h4>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6 class="fw-bold text-primary">Application Details</h6>
                                <table class="table table-sm">
                                    <tr>
                                        <td><strong>Reference Number:</strong></td>
                                        <td id="result_reference">-</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Student Name:</strong></td>
                                        <td id="result_name">-</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Submitted Date:</strong></td>
                                        <td id="result_date">-</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Current Status:</strong></td>
                                        <td id="result_status">-</td>
                                    </tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <h6 class="fw-bold text-primary">Next Steps</h6>
                                <div id="next_steps">
                                    <!-- Dynamic content based on status -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Help Section -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-question-circle me-2"></i>
                            Need Help?
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6 class="fw-bold">Can't find your reference number?</h6>
                                <ul class="small">
                                    <li>Check your email confirmation</li>
                                    <li>Look for format: SI-YYYY-XXXXXX</li>
                                    <li>Contact support if you can't locate it</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6 class="fw-bold">Contact Information</h6>
                                <p class="small mb-2">
                                    <i class="fas fa-envelope me-2"></i>
                                    <a href="mailto:<EMAIL>"><EMAIL></a>
                                </p>
                                <p class="small mb-2">
                                    <i class="fas fa-phone me-2"></i>
                                    +677 XXX-XXXX
                                </p>
                                <p class="small mb-0">
                                    <i class="fas fa-clock me-2"></i>
                                    Office Hours: 8:00 AM - 5:00 PM
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <?php include 'includes/footer.php'; ?>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        document.getElementById('statusForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const referenceNumber = document.getElementById('reference_number').value.trim();
            const studentCode = document.getElementById('student_code').value.trim();
            
            if (!referenceNumber || !studentCode) {
                alert('Please fill in both fields');
                return;
            }
            
            // Show loading state
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Searching...';
            submitBtn.disabled = true;
            
            // Simulate API call (replace with actual AJAX call)
            setTimeout(() => {
                // Mock data - replace with actual API call
                const mockData = {
                    reference: referenceNumber,
                    name: 'John Doe',
                    date: 'December 15, 2024',
                    status: 'Under Review'
                };
                
                displayResult(mockData);
                
                // Reset button
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            }, 2000);
        });
        
        function displayResult(data) {
            // Populate result data
            document.getElementById('result_reference').textContent = data.reference;
            document.getElementById('result_name').textContent = data.name;
            document.getElementById('result_date').textContent = data.date;
            
            // Status with badge
            const statusElement = document.getElementById('result_status');
            const statusClass = getStatusClass(data.status);
            statusElement.innerHTML = `<span class="badge ${statusClass}">${data.status}</span>`;
            
            // Next steps based on status
            const nextStepsElement = document.getElementById('next_steps');
            nextStepsElement.innerHTML = getNextSteps(data.status);
            
            // Show result
            document.getElementById('statusResult').style.display = 'block';
            
            // Scroll to result
            document.getElementById('statusResult').scrollIntoView({ 
                behavior: 'smooth', 
                block: 'start' 
            });
        }
        
        function getStatusClass(status) {
            switch(status) {
                case 'Selected': return 'bg-success';
                case 'Pending': return 'bg-warning';
                case 'Under Review': return 'bg-info';
                case 'Rejected': return 'bg-danger';
                default: return 'bg-secondary';
            }
        }
        
        function getNextSteps(status) {
            switch(status) {
                case 'Selected':
                    return `
                        <div class="alert alert-success">
                            <h6><i class="fas fa-check-circle me-2"></i>Congratulations!</h6>
                            <p class="small mb-0">You have been selected. Check your email for further instructions.</p>
                        </div>
                    `;
                case 'Pending':
                    return `
                        <div class="alert alert-warning">
                            <h6><i class="fas fa-clock me-2"></i>Application Pending</h6>
                            <p class="small mb-0">Your application is in queue for review. Please wait for updates.</p>
                        </div>
                    `;
                case 'Under Review':
                    return `
                        <div class="alert alert-info">
                            <h6><i class="fas fa-eye me-2"></i>Under Review</h6>
                            <p class="small mb-0">Our team is currently reviewing your application. You will be notified of any updates.</p>
                        </div>
                    `;
                case 'Rejected':
                    return `
                        <div class="alert alert-danger">
                            <h6><i class="fas fa-times-circle me-2"></i>Application Not Selected</h6>
                            <p class="small mb-0">Unfortunately, your application was not selected this time. You may apply again next year.</p>
                        </div>
                    `;
                default:
                    return '<p class="small text-muted">Status information not available.</p>';
            }
        }
    </script>
</body>
</html>
