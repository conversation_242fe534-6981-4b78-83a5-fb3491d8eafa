# Solomon Islands Scholarship Application - Apache Configuration

# Enable URL Rewriting
RewriteEngine On

# Force HTTPS (uncomment in production with SSL)
# RewriteCond %{HTTPS} off
# RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# Security Headers
<IfModule mod_headers.c>
    # Prevent MIME type sniffing
    Header always set X-Content-Type-Options nosniff
    
    # Prevent clickjacking
    Header always set X-Frame-Options DENY
    
    # XSS Protection
    Header always set X-XSS-Protection "1; mode=block"
    
    # Referrer Policy
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
    
    # Content Security Policy (adjust as needed)
    Header always set Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' cdn.jsdelivr.net cdnjs.cloudflare.com code.jquery.com; style-src 'self' 'unsafe-inline' cdn.jsdelivr.net cdnjs.cloudflare.com; font-src 'self' cdnjs.cloudflare.com; img-src 'self' data:; connect-src 'self';"
    
    # HSTS (uncomment in production with SSL)
    # Header always set Strict-Transport-Security "max-age=63072000; includeSubDomains; preload"
</IfModule>

# Protect sensitive files
<Files ".env">
    Order allow,deny
    Deny from all
</Files>

<Files ".env.*">
    Order allow,deny
    Deny from all
</Files>

<Files "config.php">
    Order allow,deny
    Deny from all
</Files>

<Files "env_loader.php">
    Order allow,deny
    Deny from all
</Files>

<Files "*.log">
    Order allow,deny
    Deny from all
</Files>

<Files "*.sql">
    Order allow,deny
    Deny from all
</Files>

<Files "*.backup">
    Order allow,deny
    Deny from all
</Files>

# Protect directories
<DirectoryMatch "^.*/logs/">
    Order allow,deny
    Deny from all
</DirectoryMatch>

<DirectoryMatch "^.*/uploads/">
    # Allow access to uploaded files but prevent script execution
    <FilesMatch "\.(php|php3|php4|php5|phtml|pl|py|jsp|asp|sh|cgi)$">
        Order allow,deny
        Deny from all
    </FilesMatch>
</DirectoryMatch>

# Prevent access to version control files
<DirectoryMatch "^.*\.git/">
    Order allow,deny
    Deny from all
</DirectoryMatch>

<Files ".gitignore">
    Order allow,deny
    Deny from all
</Files>

# Prevent access to composer files
<Files "composer.json">
    Order allow,deny
    Deny from all
</Files>

<Files "composer.lock">
    Order allow,deny
    Deny from all
</Files>

# Cache static assets
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/svg+xml "access plus 1 month"
    ExpiresByType font/woff "access plus 1 month"
    ExpiresByType font/woff2 "access plus 1 month"
</IfModule>

# Compress files
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# Prevent server signature disclosure
ServerTokens Prod

# Custom error pages (create these files if needed)
# ErrorDocument 404 /error_pages/404.html
# ErrorDocument 403 /error_pages/403.html
# ErrorDocument 500 /error_pages/500.html

# Disable server-side includes
Options -Includes

# Disable directory browsing
Options -Indexes

# Disable MultiViews
Options -MultiViews

# Follow symbolic links (if needed)
# Options +FollowSymLinks
