<?php
require_once 'config.php';

// Generate CSRF token
$csrf_token = generateCSRFToken();

// Get any error or success messages
$error_message = getErrorMessage();
$success_message = getSuccessMessage();

// Province options
$provinces = [
    'Central', 'Choiseul', 'Guadalcanal', 'Isabel', 'Makira-Ulawa', 
    'Malaita', 'Rennell and Bellona', 'Temotu', 'Western', 'Honiara'
];

// HEI options
$hei_options = [
    'University of Papua New Guinea (UPNG)', 
    'University of Technology (UOT)', 
    'Papua New Guinea University of Natural Resources and Environment (PNGUNRE)',
    'Divine Word University (DWU)',
    'Pacific Adventist University (PAU)',
    'University of Goroka (UOG)'
];

// PNG Province options
$png_provinces = [
    'National Capital District (NCD)', 'Morobe', 'Western Highlands', 'Eastern Highlands',
    'Madang', 'East New Britain', 'West New Britain', 'Manus', 'New Ireland',
    'Bougainville', 'Gulf', 'Central', 'Milne Bay', 'Oro', 'Southern Highlands',
    'Hela', 'Jiwaka', 'Chimbu', 'Western', 'Enga'
];
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo APP_NAME; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="assets/style.css" rel="stylesheet">
</head>
<body>
    <!-- Header -->
    <header class="bg-primary text-white py-4">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="h3 mb-0">
                        <i class="fas fa-graduation-cap me-2"></i>
                        Solomon Islands Scholarship Application
                    </h1>
                    <p class="mb-0 small">Papua New Guinea Higher Education Program</p>
                </div>
                <div class="col-md-4 text-md-end">
                    <img src="assets/solomon-flag.png" alt="Solomon Islands Flag" class="flag-img" style="height: 40px;">
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="container my-5">
        <!-- Alert Messages -->
        <?php if ($error_message): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <?php echo htmlspecialchars($error_message); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if ($success_message): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle me-2"></i>
                <?php echo htmlspecialchars($success_message); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- Application Form -->
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="card shadow">
                    <div class="card-header bg-light">
                        <h2 class="card-title mb-0">
                            <i class="fas fa-file-alt me-2"></i>
                            Scholarship Application Form
                        </h2>
                        <p class="text-muted mb-0 small">Please fill out all required fields marked with *</p>
                    </div>
                    
                    <div class="card-body">
                        <form id="scholarshipForm" action="submit.php" method="POST" novalidate>
                            <!-- CSRF Token -->
                            <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">
                            
                            <!-- Progress Bar -->
                            <div class="progress mb-4" style="height: 6px;">
                                <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                            </div>

                            <!-- Step 1: Basic Information -->
                            <div class="form-step active" data-step="1">
                                <h4 class="text-primary mb-4">
                                    <i class="fas fa-user me-2"></i>
                                    Step 1: Basic Information
                                </h4>
                                
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="student_code" class="form-label">
                                            Student Code <span class="text-danger">*</span>
                                        </label>
                                        <input type="text" class="form-control" id="student_code" name="student_code" 
                                               required maxlength="20" placeholder="Enter your student code">
                                        <div class="invalid-feedback">Please provide a valid student code.</div>
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <label for="selection_status" class="form-label">
                                            Selection Status <span class="text-danger">*</span>
                                        </label>
                                        <select class="form-select" id="selection_status" name="selection_status" required>
                                            <option value="">Choose status...</option>
                                            <option value="Selected">Selected</option>
                                            <option value="Pending">Pending</option>
                                            <option value="Under Review">Under Review</option>
                                        </select>
                                        <div class="invalid-feedback">Please select your selection status.</div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-8 mb-3">
                                        <label for="full_name" class="form-label">
                                            Full Name <span class="text-danger">*</span>
                                        </label>
                                        <input type="text" class="form-control" id="full_name" name="full_name" 
                                               required maxlength="100" placeholder="Enter your full name">
                                        <div class="invalid-feedback">Please provide your full name.</div>
                                    </div>
                                    
                                    <div class="col-md-4 mb-3">
                                        <label for="date_of_birth" class="form-label">Date of Birth</label>
                                        <input type="date" class="form-control" id="date_of_birth" name="date_of_birth">
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-4 mb-3">
                                        <label for="gender" class="form-label">Gender</label>
                                        <select class="form-select" id="gender" name="gender">
                                            <option value="">Choose...</option>
                                            <option value="Male">Male</option>
                                            <option value="Female">Female</option>
                                            <option value="Other">Other</option>
                                        </select>
                                    </div>
                                    
                                    <div class="col-md-4 mb-3">
                                        <label for="email" class="form-label">Email Address</label>
                                        <input type="email" class="form-control" id="email" name="email" 
                                               maxlength="100" placeholder="<EMAIL>">
                                        <div class="invalid-feedback">Please provide a valid email address.</div>
                                    </div>
                                    
                                    <div class="col-md-4 mb-3">
                                        <label for="phone" class="form-label">Phone Number</label>
                                        <input type="tel" class="form-control" id="phone" name="phone" 
                                               maxlength="20" placeholder="+677 XXXXXXX">
                                        <div class="invalid-feedback">Please provide a valid phone number.</div>
                                    </div>
                                </div>
                            </div>

                            <!-- Step 2: Location Information -->
                            <div class="form-step" data-step="2">
                                <h4 class="text-primary mb-4">
                                    <i class="fas fa-map-marker-alt me-2"></i>
                                    Step 2: Location Information
                                </h4>

                                <div class="mb-4">
                                    <label class="form-label">
                                        Province of Origin <span class="text-danger">*</span>
                                    </label>
                                    <div class="row">
                                        <?php foreach ($provinces as $province): ?>
                                            <div class="col-md-4 col-sm-6 mb-2">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox"
                                                           name="province_of_origin[]" value="<?php echo $province; ?>"
                                                           id="origin_<?php echo str_replace(' ', '_', $province); ?>">
                                                    <label class="form-check-label"
                                                           for="origin_<?php echo str_replace(' ', '_', $province); ?>">
                                                        <?php echo $province; ?>
                                                    </label>
                                                </div>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                    <div class="invalid-feedback d-block" id="province_origin_error" style="display: none !important;">
                                        Please select at least one province of origin.
                                    </div>
                                </div>

                                <div class="mb-4">
                                    <label class="form-label">Current Residential Province</label>
                                    <div class="row">
                                        <?php foreach ($provinces as $province): ?>
                                            <div class="col-md-4 col-sm-6 mb-2">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox"
                                                           name="current_residential_province[]" value="<?php echo $province; ?>"
                                                           id="current_<?php echo str_replace(' ', '_', $province); ?>">
                                                    <label class="form-check-label"
                                                           for="current_<?php echo str_replace(' ', '_', $province); ?>">
                                                        <?php echo $province; ?>
                                                    </label>
                                                </div>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                </div>
                            </div>

                            <!-- Step 3: Transport Information -->
                            <div class="form-step" data-step="3">
                                <h4 class="text-primary mb-4">
                                    <i class="fas fa-plane me-2"></i>
                                    Step 3: Transport Information
                                </h4>

                                <div class="mb-4">
                                    <label class="form-label">
                                        Mode of Transport to Honiara <span class="text-danger">*</span>
                                    </label>
                                    <div class="row">
                                        <div class="col-md-4 mb-2">
                                            <div class="form-check">
                                                <input class="form-check-input" type="radio" name="mode_of_transport_to_honiara"
                                                       value="Sea" id="transport_sea" required>
                                                <label class="form-check-label" for="transport_sea">
                                                    <i class="fas fa-ship me-2"></i>Sea
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col-md-4 mb-2">
                                            <div class="form-check">
                                                <input class="form-check-input" type="radio" name="mode_of_transport_to_honiara"
                                                       value="Land" id="transport_land" required>
                                                <label class="form-check-label" for="transport_land">
                                                    <i class="fas fa-car me-2"></i>Land
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col-md-4 mb-2">
                                            <div class="form-check">
                                                <input class="form-check-input" type="radio" name="mode_of_transport_to_honiara"
                                                       value="Air" id="transport_air" required>
                                                <label class="form-check-label" for="transport_air">
                                                    <i class="fas fa-plane me-2"></i>Air
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="invalid-feedback">Please select a mode of transport.</div>
                                </div>

                                <div class="mb-4">
                                    <label for="honiara_pom_route" class="form-label">Honiara-POM Route Details</label>
                                    <input type="text" class="form-control" id="honiara_pom_route" name="honiara_pom_route"
                                           maxlength="100" placeholder="Describe your preferred route from Honiara to Port Moresby">
                                    <div class="form-text">Optional: Specify any preferred routing or travel arrangements</div>
                                </div>

                                <div class="mb-4">
                                    <label class="form-label">Travel Route Preference</label>
                                    <div class="row">
                                        <div class="col-md-6 mb-2">
                                            <div class="form-check">
                                                <input class="form-check-input" type="radio" name="travel_question"
                                                       value="sea-air-air" id="route_sea_air">
                                                <label class="form-check-label" for="route_sea_air">
                                                    Sea → Air → Air
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col-md-6 mb-2">
                                            <div class="form-check">
                                                <input class="form-check-input" type="radio" name="travel_question"
                                                       value="land-air-air" id="route_land_air">
                                                <label class="form-check-label" for="route_land_air">
                                                    Land → Air → Air
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col-md-6 mb-2">
                                            <div class="form-check">
                                                <input class="form-check-input" type="radio" name="travel_question"
                                                       value="air-direct" id="route_air_direct">
                                                <label class="form-check-label" for="route_air_direct">
                                                    Air (Direct)
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col-md-6 mb-2">
                                            <div class="form-check">
                                                <input class="form-check-input" type="radio" name="travel_question"
                                                       value="other" id="route_other">
                                                <label class="form-check-label" for="route_other">
                                                    Other
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Step 4: Education Information -->
                            <div class="form-step" data-step="4">
                                <h4 class="text-primary mb-4">
                                    <i class="fas fa-university me-2"></i>
                                    Step 4: Education Information
                                </h4>

                                <div class="mb-4">
                                    <label class="form-label">
                                        Higher Education Institution (HEI) <span class="text-danger">*</span>
                                    </label>
                                    <div class="row">
                                        <?php foreach ($hei_options as $hei): ?>
                                            <div class="col-md-6 mb-2">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox"
                                                           name="hei_name[]" value="<?php echo $hei; ?>"
                                                           id="hei_<?php echo str_replace([' ', '(', ')'], ['_', '', ''], $hei); ?>">
                                                    <label class="form-check-label"
                                                           for="hei_<?php echo str_replace([' ', '(', ')'], ['_', '', ''], $hei); ?>">
                                                        <?php echo $hei; ?>
                                                    </label>
                                                </div>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                    <div class="invalid-feedback d-block" id="hei_error" style="display: none !important;">
                                        Please select at least one Higher Education Institution.
                                    </div>
                                </div>

                                <div class="mb-4">
                                    <label class="form-label">
                                        PNG Province (Study Destination) <span class="text-danger">*</span>
                                    </label>
                                    <select class="form-select" name="png_province" id="png_province" required>
                                        <option value="">Choose PNG Province...</option>
                                        <?php foreach ($png_provinces as $province): ?>
                                            <option value="<?php echo $province; ?>"><?php echo $province; ?></option>
                                        <?php endforeach; ?>
                                    </select>
                                    <div class="invalid-feedback">Please select a PNG province.</div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="course_of_study" class="form-label">Course of Study</label>
                                        <input type="text" class="form-control" id="course_of_study" name="course_of_study"
                                               maxlength="200" placeholder="e.g., Bachelor of Engineering">
                                        <div class="form-text">Specify your intended field of study</div>
                                    </div>

                                    <div class="col-md-6 mb-3">
                                        <label for="estimated_cost" class="form-label">Estimated Cost (USD)</label>
                                        <div class="input-group">
                                            <span class="input-group-text">$</span>
                                            <input type="number" class="form-control" id="estimated_cost" name="estimated_cost"
                                                   min="0" step="0.01" placeholder="0.00">
                                        </div>
                                        <div class="form-text">Estimated total cost for your education</div>
                                    </div>
                                </div>

                                <div class="mb-4">
                                    <label for="academic_qualifications" class="form-label">Academic Qualifications</label>
                                    <textarea class="form-control" id="academic_qualifications" name="academic_qualifications"
                                              rows="4" placeholder="List your academic qualifications, certificates, and achievements..."></textarea>
                                    <div class="form-text">Include your educational background, certificates, and relevant qualifications</div>
                                </div>
                            </div>

                            <!-- Step 5: Final Information -->
                            <div class="form-step" data-step="5">
                                <h4 class="text-primary mb-4">
                                    <i class="fas fa-file-signature me-2"></i>
                                    Step 5: Final Information
                                </h4>

                                <div class="mb-4">
                                    <label for="motivation_letter" class="form-label">Motivation Letter</label>
                                    <textarea class="form-control" id="motivation_letter" name="motivation_letter"
                                              rows="6" placeholder="Explain why you want to study in Papua New Guinea and how this scholarship will help you achieve your goals..."></textarea>
                                    <div class="form-text">Tell us about your motivation, goals, and how this scholarship will benefit you and Solomon Islands</div>
                                </div>

                                <!-- Terms and Conditions -->
                                <div class="card bg-light mb-4">
                                    <div class="card-body">
                                        <h6 class="card-title">
                                            <i class="fas fa-info-circle me-2"></i>
                                            Terms and Conditions
                                        </h6>
                                        <ul class="small mb-3">
                                            <li>All information provided must be accurate and truthful</li>
                                            <li>Data submitted will be used solely for scholarship processing by the PNG Government</li>
                                            <li>False information may result in disqualification</li>
                                            <li>The scholarship program reserves the right to verify all information provided</li>
                                            <li>Selected candidates may be required to provide additional documentation</li>
                                        </ul>

                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="terms_agreement" name="terms_agreement" required>
                                            <label class="form-check-label" for="terms_agreement">
                                                I agree to the terms and conditions and confirm that all information provided is accurate <span class="text-danger">*</span>
                                            </label>
                                            <div class="invalid-feedback">You must agree to the terms and conditions.</div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Application Summary -->
                                <div class="card border-success mb-4">
                                    <div class="card-header bg-success text-white">
                                        <h6 class="mb-0">
                                            <i class="fas fa-check-circle me-2"></i>
                                            Application Summary
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        <p class="mb-2"><strong>Please review your information before submitting:</strong></p>
                                        <ul class="small text-muted">
                                            <li>Ensure all required fields are completed</li>
                                            <li>Verify that your contact information is correct</li>
                                            <li>Double-check your academic and personal details</li>
                                            <li>Make sure you have selected the appropriate institutions and provinces</li>
                                        </ul>
                                        <div class="alert alert-info small mb-0">
                                            <i class="fas fa-lightbulb me-2"></i>
                                            <strong>Tip:</strong> After submission, you will receive a confirmation with your application reference number. Keep this for your records.
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Navigation Buttons -->
                            <div class="d-flex justify-content-between mt-4">
                                <button type="button" class="btn btn-secondary" id="prevBtn" style="display: none;">
                                    <i class="fas fa-arrow-left me-2"></i>Previous
                                </button>
                                <button type="button" class="btn btn-primary" id="nextBtn">
                                    Next<i class="fas fa-arrow-right ms-2"></i>
                                </button>
                                <button type="submit" class="btn btn-success" id="submitBtn" style="display: none;">
                                    <i class="fas fa-paper-plane me-2"></i>Submit Application
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="bg-dark text-white py-4 mt-5">
        <div class="container text-center">
            <p class="mb-0">&copy; 2024 Solomon Islands Government - Papua New Guinea Scholarship Program</p>
            <p class="small text-muted">All data submitted will be used solely for scholarship processing.</p>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="assets/script.js"></script>
</body>
</html>
