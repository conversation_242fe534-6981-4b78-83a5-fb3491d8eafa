# Solomon Islands Scholarship Application - Environment Configuration Template
# Copy this file to .env and update with your actual values

# Database Configuration
DB_HOST=localhost
DB_USERNAME=your_database_username
DB_PASSWORD=your_database_password
DB_NAME=your_database_name

# Application Configuration
APP_NAME="Solomon Islands Scholarship Application"
APP_VERSION=1.0.0
APP_ENV=production
APP_DEBUG=false

# Security Configuration
CSRF_TOKEN_NAME=csrf_token
SESSION_TIMEOUT=3600

# Email Configuration (Optional - Configure if you want email functionality)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your_email_password
FROM_EMAIL=<EMAIL>
FROM_NAME="Solomon Islands Scholarship Program"
ADMIN_EMAIL=<EMAIL>

# File Upload Configuration
MAX_FILE_SIZE=5242880
UPLOAD_DIR=uploads/

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=logs/application.log

# Admin Configuration
ADMIN_SESSION_TIMEOUT=7200
DEFAULT_ADMIN_USERNAME=admin
DEFAULT_ADMIN_PASSWORD_HASH=$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi

# Timezone
APP_TIMEZONE=Pacific/Guadalcanal
